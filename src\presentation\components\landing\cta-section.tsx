"use client";

import { <PERSON><PERSON> } from "@/src/presentation/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>rkles, Shield, Zap } from "lucide-react";

export function CTASection() {
  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-accent/5" />
      <div className="absolute top-0 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000" />
      
      <div className="max-w-4xl mx-auto text-center relative">
        {/* Badge */}
        <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-sm text-primary mb-8 animate-fade-in">
          <Sparkles className="w-4 h-4" />
          Limited Time Offer
        </div>

        {/* Main heading */}
        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6 animate-fade-in-up">
          <span className="bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Ready to Transform Your
          </span>
          <br />
          <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Investment Strategy?
          </span>
        </h2>

        {/* Subtitle */}
        <p className="text-xl text-muted-foreground mb-8 animate-fade-in-up delay-200">
          Join thousands of successful investors who trust Arthik.io for their portfolio management needs.
          Start your free trial today and experience the future of trading.
        </p>

        {/* Feature highlights */}
        <div className="flex flex-wrap justify-center gap-6 mb-12 animate-fade-in-up delay-300">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Shield className="w-4 h-4 text-green-500" />
            30-day free trial
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Zap className="w-4 h-4 text-blue-500" />
            No setup fees
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Sparkles className="w-4 h-4 text-purple-500" />
            Cancel anytime
          </div>
        </div>

        {/* CTA buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8 animate-fade-in-up delay-400">
          <Button size="lg" className="group text-lg px-8 py-6">
            Start Free Trial
            <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
          </Button>
          <Button variant="outline" size="lg" className="text-lg px-8 py-6">
            Schedule Demo
          </Button>
        </div>

        {/* Trust indicators */}
        <div className="animate-fade-in-up delay-500">
          <p className="text-sm text-muted-foreground mb-4">
            Trusted by 10,000+ investors • SOC 2 Compliant • Bank-level Security
          </p>
          <div className="flex justify-center items-center gap-2 text-xs text-muted-foreground">
            <Shield className="w-4 h-4" />
            <span>Your data is protected with enterprise-grade encryption</span>
          </div>
        </div>
      </div>
    </section>
  );
}
